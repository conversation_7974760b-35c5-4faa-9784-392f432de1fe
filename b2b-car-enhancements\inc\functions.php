<?php
if( ! function_exists( 'get_plugin_data' ) ) {
	require_once( ABSPATH . 'wp-admin/includes/plugin.php' );
}
use <PERSON><PERSON>zen\B2B_Car_Enhancements\Helper;

/**
 * Gets the site's base URL
 * 
 * @uses get_bloginfo()
 * 
 * @return string $url the site URL
 */
if( ! function_exists( 'pc_site_url' ) ) :
function pc_site_url() {
	$url = get_bloginfo( 'url' );

	return $url;
}
endif;

// um_client

// 3. Add column to Users admin panel
add_filter('manage_users_columns', function ($columns) {
    $columns['user_approved'] 	= 'Approval Status';
    $columns['approval_action'] = 'Action';
    return $columns;
});

add_filter('manage_users_custom_column', function ($value, $column_name, $user_id) {
    if ($column_name == 'user_approved') {
        $approved = get_user_meta($user_id, 'new_user_approve', true);
        // $approved = get_user_meta($user_id, '_user_approved', true);
        return $approved === 'approved' ? '✅ Approved' : '❌ Pending';
    }
    return $value;
}, 10, 3);

// 4. Add Approve/Reject bulk actions
add_filter('bulk_actions-users', function ($bulk_actions) {
    $bulk_actions['approve_users'] = 'Approve Users';
    $bulk_actions['reject_users'] = 'Reject Users';
    return $bulk_actions;
});

add_filter('handle_bulk_actions-users', function ($redirect_to, $doaction, $user_ids) {
    if ($doaction === 'approve_users') {
        foreach ($user_ids as $user_id) {
            update_user_meta($user_id, 'new_user_approve', 'approved');
            update_user_meta($user_id, 'approval_status', 'approved');
            update_user_meta($user_id, 'account_status', 'approved');
            // update_user_meta($user_id, '_user_approved', 'yes');
        }
        $redirect_to = add_query_arg('approved_users', count($user_ids), $redirect_to);
    }
    if ($doaction === 'reject_users') {
        foreach ($user_ids as $user_id) {
            wp_delete_user($user_id); // Or just update meta to rejected if you prefer
        }
        $redirect_to = add_query_arg('rejected_users', count($user_ids), $redirect_to);
    }
    return $redirect_to;
}, 10, 3);

// 6. Display Approve/Reject buttons in the new column
add_filter('manage_users_custom_column', function ($value, $column_name, $user_id) {
    if ($column_name === 'approval_action') {
        $status = get_user_meta($user_id, 'new_user_approve', true);
        // $status = get_user_meta($user_id, '_user_approved', true);

        if ($status !== 'approved') {
            $approve_url = wp_nonce_url(add_query_arg([
                'action' => 'approve_user',
                'user_id' => $user_id,
            ]), 'approve_user_' . $user_id);

            $reject_url = wp_nonce_url(add_query_arg([
                'action' => 'reject_user',
                'user_id' => $user_id,
            ]), 'reject_user_' . $user_id);

            return '<a href="' . esc_url($approve_url) . '" class="button button-primary" style="margin-right:5px;">Approve</a>' .
                   '<a href="' . esc_url($reject_url) . '" class="button button-secondary">Reject</a>';
        } else {
            return '<span style="color: green; font-weight: bold;">Approved</span>';
        }
    }

    return $value;
}, 10, 3);

// 7. Handle Approve and Reject actions
add_action('admin_init', function () {
    if (!current_user_can('edit_users')) {
        return;
    }

    if (isset($_GET['action'], $_GET['user_id']) && $_GET['action'] === 'approve_user') {
        $user_id = intval($_GET['user_id']);
        if (wp_verify_nonce($_GET['_wpnonce'], 'approve_user_' . $user_id)) {
            update_user_meta($user_id, 'new_user_approve', 'approved');
            update_user_meta($user_id, 'approval_status', 'approved');
            update_user_meta($user_id, 'account_status', 'approved');
            // update_user_meta($user_id, '_user_approved', 'yes');
            wp_redirect(add_query_arg(['user_approved_single' => 1], remove_query_arg(['action', 'user_id', '_wpnonce'])));
            exit;
        }
    }

    if (isset($_GET['action'], $_GET['user_id']) && $_GET['action'] === 'reject_user') {
        $user_id = intval($_GET['user_id']);
        if (wp_verify_nonce($_GET['_wpnonce'], 'reject_user_' . $user_id)) {
            wp_delete_user($user_id);
            wp_redirect(add_query_arg(['user_rejected_single' => 1], remove_query_arg(['action', 'user_id', '_wpnonce'])));
            exit;
        }
    }
});

// 8. Admin notice for single approve/reject
add_action('admin_notices', function () {
    if (isset($_GET['user_approved_single'])) {
        echo '<div class="notice notice-success is-dismissible"><p>User approved successfully.</p></div>';
    }
    if (isset($_GET['user_rejected_single'])) {
        echo '<div class="notice notice-warning is-dismissible"><p>User rejected and deleted successfully.</p></div>';
    }
});

function add_xoo_el_registration_fields( $fields ) {

    // Ensure $fields is an array
    if ( ! is_array( $fields ) ) {
        $fields = [];
    }

    // Add Phone Number field
    $fields['xoo_el_reg_b2b_phone'] = [
        'field_type' => 'xoo_el_reg_b2b_phone',
        'input_type' => 'text',
        'settings'   => [
            'active'      => 'yes',
            'required'    => 'yes',
            'label'       => __( 'Phone Number', 'b2b-xoo-el-enhancements' ),
            'placeholder' => 'Enter your phone number',
            'cols'        => 'one',
            'icon'        => 'fas fa-phone',
            'class'       => '',
            'unique_id'   => 'xoo_el_reg_b2b_phone',
            'minlength'   => '',
            'maxlength'   => '',
        ],
        'priority' => 90,
    ];

    // Add VAT Number field
    $fields['xoo_el_reg_b2b_vat'] = [
        'field_type' => 'xoo_el_reg_b2b_vat',
        'input_type' => 'text',
        'settings'   => [
            'active'      => 'yes',
            'required'    => 'yes',
            'label'       => __( 'VAT Number', 'b2b-xoo-el-enhancements' ),
            'placeholder' => 'Enter your VAT number',
            'cols'        => 'one',
            'icon'        => 'fas fa-file-invoice',
            'class'       => '',
            'unique_id'   => 'xoo_el_reg_b2b_vat',
            'minlength'   => '',
            'maxlength'   => '',
        ],
        'priority' => 91,
    ];

    // Add Company Name field
    $fields['xoo_el_reg_b2b_company'] = [
        'field_type' => 'xoo_el_reg_b2b_company',
        'input_type' => 'text',
        'settings'   => [
            'active'      => 'yes',
            'required'    => 'yes',
            'label'       => __( 'Commercial Name', 'b2b-xoo-el-enhancements' ),
            'placeholder' => 'Enter your company name',
            'cols'        => 'one',
            'icon'        => 'fas fa-building',
            'class'       => '',
            'unique_id'   => 'xoo_el_reg_b2b_company',
            'minlength'   => '',
            'maxlength'   => '',
        ],
        'priority' => 92,
    ];

    return $fields;
}


add_filter( 'xoo_el_register_fields', 'add_xoo_el_registration_fields' );

// Hook into the field data retrieval to add our custom fields
function add_custom_fields_to_data( $data ) {
    if ( ! is_array( $data ) ) {
        $data = [];
    }

    // Add our custom fields to the data array
    $custom_fields = [
        'xoo_el_reg_b2b_phone' => [
            'field_type' => 'xoo_el_reg_b2b_phone',
            'input_type' => 'text',
            'settings'   => [
                'active'      => 'yes',
                'required'    => 'yes',
                'label'       => __( 'Phone Number', 'b2b-xoo-el-enhancements' ),
                'placeholder' => 'Enter your phone number',
                'cols'        => 'one',
                'icon'        => 'fas fa-phone',
                'class'       => '',
                'unique_id'   => 'xoo_el_reg_b2b_phone',
                'minlength'   => '',
                'maxlength'   => '',
            ],
            'priority' => 90,
        ],
        'xoo_el_reg_b2b_vat' => [
            'field_type' => 'xoo_el_reg_b2b_vat',
            'input_type' => 'text',
            'settings'   => [
                'active'      => 'yes',
                'required'    => 'yes',
                'label'       => __( 'VAT Number', 'b2b-xoo-el-enhancements' ),
                'placeholder' => 'Enter your VAT number',
                'cols'        => 'one',
                'icon'        => 'fas fa-file-invoice',
                'class'       => '',
                'unique_id'   => 'xoo_el_reg_b2b_vat',
                'minlength'   => '',
                'maxlength'   => '',
            ],
            'priority' => 91,
        ],
        'xoo_el_reg_b2b_company' => [
            'field_type' => 'xoo_el_reg_b2b_company',
            'input_type' => 'text',
            'settings'   => [
                'active'      => 'yes',
                'required'    => 'yes',
                'label'       => __( 'Commercial Name', 'b2b-xoo-el-enhancements' ),
                'placeholder' => 'Enter your company name',
                'cols'        => 'one',
                'icon'        => 'fas fa-building',
                'class'       => '',
                'unique_id'   => 'xoo_el_reg_b2b_company',
                'minlength'   => '',
                'maxlength'   => '',
            ],
            'priority' => 92,
        ],
    ];

    // Merge custom fields with existing data
    $data = array_merge( $data, $custom_fields );

    return $data;
}

add_filter( 'xoo_aff_xoo-el_data', 'add_custom_fields_to_data' );

// Add individual field data filters for each custom field
function get_custom_field_data_phone( $data, $field_id ) {
    if ( $field_id === 'xoo_el_reg_b2b_phone' && ! $data ) {
        return [
            'field_type' => 'xoo_el_reg_b2b_phone',
            'input_type' => 'text',
            'settings'   => [
                'active'      => 'yes',
                'required'    => 'yes',
                'label'       => __( 'Phone Number', 'b2b-xoo-el-enhancements' ),
                'placeholder' => 'Enter your phone number',
                'cols'        => 'one',
                'icon'        => 'fas fa-phone',
                'class'       => '',
                'unique_id'   => 'xoo_el_reg_b2b_phone',
                'minlength'   => '',
                'maxlength'   => '',
            ],
            'priority' => 90,
        ];
    }
    return $data;
}

function get_custom_field_data_vat( $data, $field_id ) {
    if ( $field_id === 'xoo_el_reg_b2b_vat' && ! $data ) {
        return [
            'field_type' => 'xoo_el_reg_b2b_vat',
            'input_type' => 'text',
            'settings'   => [
                'active'      => 'yes',
                'required'    => 'yes',
                'label'       => __( 'VAT Number', 'b2b-xoo-el-enhancements' ),
                'placeholder' => 'Enter your VAT number',
                'cols'        => 'one',
                'icon'        => 'fas fa-file-invoice',
                'class'       => '',
                'unique_id'   => 'xoo_el_reg_b2b_vat',
                'minlength'   => '',
                'maxlength'   => '',
            ],
            'priority' => 91,
        ];
    }
    return $data;
}

function get_custom_field_data_company( $data, $field_id ) {
    if ( $field_id === 'xoo_el_reg_b2b_company' && ! $data ) {
        return [
            'field_type' => 'xoo_el_reg_b2b_company',
            'input_type' => 'text',
            'settings'   => [
                'active'      => 'yes',
                'required'    => 'yes',
                'label'       => __( 'Commercial Name', 'b2b-xoo-el-enhancements' ),
                'placeholder' => 'Enter your company name',
                'cols'        => 'one',
                'icon'        => 'fas fa-building',
                'class'       => '',
                'unique_id'   => 'xoo_el_reg_b2b_company',
                'minlength'   => '',
                'maxlength'   => '',
            ],
            'priority' => 92,
        ];
    }
    return $data;
}

add_filter( 'xoo_aff_xoo-el_xoo_el_reg_b2b_phone_data', 'get_custom_field_data_phone', 10, 2 );
add_filter( 'xoo_aff_xoo-el_xoo_el_reg_b2b_vat_data', 'get_custom_field_data_vat', 10, 2 );
add_filter( 'xoo_aff_xoo-el_xoo_el_reg_b2b_company_data', 'get_custom_field_data_company', 10, 2 );

// Properly register custom fields using the plugin's field registration system
function register_custom_b2b_fields( $fields_obj ) {

    // Register Phone Number field type
    $fields_obj->add_type(
        'xoo_el_reg_b2b_phone',
        'text',
        'B2B Phone',
        array(
            'is_selectable' => 'no',
            'can_delete'    => 'no',
            'icon'          => 'fas fa-phone',
        )
    );

    $phone_settings = array(
        'active' => array(
            'value' => 'yes'
        ),
        'required' => array(
            'value' => 'yes'
        ),
        'label' => array(
            'value' => __( 'Phone Number', 'b2b-xoo-el-enhancements' )
        ),
        'cols' => array(
            'value' => 'one'
        ),
        'icon' => array(
            'value' => 'fas fa-phone'
        ),
        'placeholder' => array(
            'value' => 'Enter your phone number'
        ),
        'unique_id' => array(
            'disabled' => 'disabled',
        ),
        'minlength',
        'maxlength',
        'class'
    );

    $fields_obj->create_field_settings( 'xoo_el_reg_b2b_phone', $phone_settings );

    $fields_obj->add_field(
        'xoo_el_reg_b2b_phone',
        'xoo_el_reg_b2b_phone',
        array(
            'unique_id' => 'xoo_el_reg_b2b_phone',
            'required'  => 'yes',
        ),
        90
    );

    // Register VAT Number field type
    $fields_obj->add_type(
        'xoo_el_reg_b2b_vat',
        'text',
        'B2B VAT',
        array(
            'is_selectable' => 'no',
            'can_delete'    => 'no',
            'icon'          => 'fas fa-file-invoice',
        )
    );

    $vat_settings = array(
        'active' => array(
            'value' => 'yes'
        ),
        'required' => array(
            'value' => 'yes'
        ),
        'label' => array(
            'value' => __( 'VAT Number', 'b2b-xoo-el-enhancements' )
        ),
        'cols' => array(
            'value' => 'one'
        ),
        'icon' => array(
            'value' => 'fas fa-file-invoice'
        ),
        'placeholder' => array(
            'value' => 'Enter your VAT number'
        ),
        'unique_id' => array(
            'disabled' => 'disabled',
        ),
        'minlength',
        'maxlength',
        'class'
    );

    $fields_obj->create_field_settings( 'xoo_el_reg_b2b_vat', $vat_settings );

    $fields_obj->add_field(
        'xoo_el_reg_b2b_vat',
        'xoo_el_reg_b2b_vat',
        array(
            'unique_id' => 'xoo_el_reg_b2b_vat',
            'required'  => 'yes',
        ),
        91
    );

    // Register Company Name field type
    $fields_obj->add_type(
        'xoo_el_reg_b2b_company',
        'text',
        'B2B Company',
        array(
            'is_selectable' => 'no',
            'can_delete'    => 'no',
            'icon'          => 'fas fa-building',
        )
    );

    $company_settings = array(
        'active' => array(
            'value' => 'yes'
        ),
        'required' => array(
            'value' => 'yes'
        ),
        'label' => array(
            'value' => __( 'Commercial Name', 'b2b-xoo-el-enhancements' )
        ),
        'cols' => array(
            'value' => 'one'
        ),
        'icon' => array(
            'value' => 'fas fa-building'
        ),
        'placeholder' => array(
            'value' => 'Enter your company name'
        ),
        'unique_id' => array(
            'disabled' => 'disabled',
        ),
        'minlength',
        'maxlength',
        'class'
    );

    $fields_obj->create_field_settings( 'xoo_el_reg_b2b_company', $company_settings );

    $fields_obj->add_field(
        'xoo_el_reg_b2b_company',
        'xoo_el_reg_b2b_company',
        array(
            'unique_id' => 'xoo_el_reg_b2b_company',
            'required'  => 'yes',
        ),
        92
    );
}

add_action( 'xoo_aff_xoo-el_add_predefined_fields', 'register_custom_b2b_fields' );

// Save custom fields when user registers
function save_xoo_el_custom_fields( $user_id, $form_data ) {

    // Save phone number
    if ( isset( $form_data['xoo_el_reg_b2b_phone'] ) ) {
        update_user_meta( $user_id, 'xoo_el_reg_b2b_phone', sanitize_text_field( $form_data['xoo_el_reg_b2b_phone'] ) );
        update_user_meta( $user_id, 'billing_phone', sanitize_text_field( $form_data['xoo_el_reg_b2b_phone'] ) );
    }

    // Save VAT number
    if ( isset( $form_data['xoo_el_reg_b2b_vat'] ) ) {
        update_user_meta( $user_id, 'xoo_el_reg_b2b_vat', sanitize_text_field( $form_data['xoo_el_reg_b2b_vat'] ) );
    }

    // Save company name
    if ( isset( $form_data['xoo_el_reg_b2b_company'] ) ) {
        update_user_meta( $user_id, 'xoo_el_reg_b2b_company', sanitize_text_field( $form_data['xoo_el_reg_b2b_company'] ) );
        update_user_meta( $user_id, 'billing_company', sanitize_text_field( $form_data['xoo_el_reg_b2b_company'] ) );
    }
}

add_action( 'xoo_el_after_register_user', 'save_xoo_el_custom_fields', 10, 2 );

// Add custom field validation
function validate_xoo_el_custom_fields( $errors, $field_id, $user_value ) {

    // Validate phone number
    if ( $field_id === 'xoo_el_reg_b2b_phone' && ! empty( $user_value ) ) {
        // Basic phone validation - remove spaces and check if it contains only numbers, +, -, (, )
        $cleaned_phone = preg_replace('/[^0-9+\-\(\)\s]/', '', $user_value);
        if ( strlen( $cleaned_phone ) < 10 ) {
            $errors->add( 'invalid_phone', __( 'Please enter a valid phone number with at least 10 digits.', 'b2b-xoo-el-enhancements' ), $field_id );
        }
    }

    // Validate VAT number
    if ( $field_id === 'xoo_el_reg_b2b_vat' && ! empty( $user_value ) ) {
        // Basic VAT validation - should be alphanumeric
        if ( ! preg_match('/^[A-Z0-9]+$/i', $user_value) ) {
            $errors->add( 'invalid_vat', __( 'VAT number should contain only letters and numbers.', 'b2b-xoo-el-enhancements' ), $field_id );
        }
    }

    // Validate company name
    if ( $field_id === 'xoo_el_reg_b2b_company' && ! empty( $user_value ) ) {
        if ( strlen( $user_value ) < 2 ) {
            $errors->add( 'invalid_company', __( 'Company name must be at least 2 characters long.', 'b2b-xoo-el-enhancements' ), $field_id );
        }
    }

    return $errors;
}

add_filter( 'xoo_aff_xoo-el_validate_field', 'validate_xoo_el_custom_fields', 10, 3 );

// Add custom fields to My Account page
function add_xoo_el_myaccount_fields( $fields ) {

    // Ensure $fields is an array
    if ( ! is_array( $fields ) ) {
        $fields = [];
    }

    // Add Phone Number field to My Account
    $fields['xoo_el_reg_b2b_phone'] = [
        'field_type' => 'xoo_el_reg_b2b_phone',
        'input_type' => 'text',
        'settings'   => [
            'active'        => 'yes',
            'required'      => 'yes',
            'label'         => __( 'Phone Number', 'b2b-xoo-el-enhancements' ),
            'placeholder'   => 'Enter your phone number',
            'cols'          => 'one',
            'icon'          => 'fas fa-phone',
            'class'         => '',
            'unique_id'     => 'xoo_el_reg_b2b_phone',
            'display_myacc' => 'yes',
            'minlength'     => '',
            'maxlength'     => '',
        ],
        'priority' => 90,
    ];

    // Add VAT Number field to My Account
    $fields['xoo_el_reg_b2b_vat'] = [
        'field_type' => 'xoo_el_reg_b2b_vat',
        'input_type' => 'text',
        'settings'   => [
            'active'        => 'yes',
            'required'      => 'yes',
            'label'         => __( 'VAT Number', 'b2b-xoo-el-enhancements' ),
            'placeholder'   => 'Enter your VAT number',
            'cols'          => 'one',
            'icon'          => 'fas fa-file-invoice',
            'class'         => '',
            'unique_id'     => 'xoo_el_reg_b2b_vat',
            'display_myacc' => 'yes',
            'minlength'     => '',
            'maxlength'     => '',
        ],
        'priority' => 91,
    ];

    // Add Company Name field to My Account
    $fields['xoo_el_reg_b2b_company'] = [
        'field_type' => 'xoo_el_reg_b2b_company',
        'input_type' => 'text',
        'settings'   => [
            'active'        => 'yes',
            'required'      => 'yes',
            'label'         => __( 'Commercial Name', 'b2b-xoo-el-enhancements' ),
            'placeholder'   => 'Enter your company name',
            'cols'          => 'one',
            'icon'          => 'fas fa-building',
            'class'         => '',
            'unique_id'     => 'xoo_el_reg_b2b_company',
            'display_myacc' => 'yes',
            'minlength'     => '',
            'maxlength'     => '',
        ],
        'priority' => 92,
    ];

    return $fields;
}

add_filter( 'xoo_el_myaccount_fields', 'add_xoo_el_myaccount_fields' );

// Save custom fields when updated from My Account page
function save_xoo_el_myaccount_custom_fields( $user_id, $form_data ) {

    // Save phone number
    if ( isset( $form_data['xoo_el_reg_b2b_phone'] ) ) {
        update_user_meta( $user_id, 'xoo_el_reg_b2b_phone', sanitize_text_field( $form_data['xoo_el_reg_b2b_phone'] ) );
        update_user_meta( $user_id, 'billing_phone', sanitize_text_field( $form_data['xoo_el_reg_b2b_phone'] ) );
    }

    // Save VAT number
    if ( isset( $form_data['xoo_el_reg_b2b_vat'] ) ) {
        update_user_meta( $user_id, 'xoo_el_reg_b2b_vat', sanitize_text_field( $form_data['xoo_el_reg_b2b_vat'] ) );
    }

    // Save company name
    if ( isset( $form_data['xoo_el_reg_b2b_company'] ) ) {
        update_user_meta( $user_id, 'xoo_el_reg_b2b_company', sanitize_text_field( $form_data['xoo_el_reg_b2b_company'] ) );
        update_user_meta( $user_id, 'billing_company', sanitize_text_field( $form_data['xoo_el_reg_b2b_company'] ) );
    }
}

add_action( 'xoo_el_after_myaccount_update', 'save_xoo_el_myaccount_custom_fields', 10, 2 );