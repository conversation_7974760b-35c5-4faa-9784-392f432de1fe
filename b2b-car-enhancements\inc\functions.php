<?php
if( ! function_exists( 'get_plugin_data' ) ) {
	require_once( ABSPATH . 'wp-admin/includes/plugin.php' );
}
use <PERSON><PERSON>zen\B2B_Car_Enhancements\Helper;

/**
 * Gets the site's base URL
 * 
 * @uses get_bloginfo()
 * 
 * @return string $url the site URL
 */
if( ! function_exists( 'pc_site_url' ) ) :
function pc_site_url() {
	$url = get_bloginfo( 'url' );

	return $url;
}
endif;

// um_client

// 3. Add column to Users admin panel
add_filter('manage_users_columns', function ($columns) {
    $columns['user_approved'] 	= 'Approval Status';
    $columns['approval_action'] = 'Action';
    return $columns;
});

add_filter('manage_users_custom_column', function ($value, $column_name, $user_id) {
    if ($column_name == 'user_approved') {
        $approved = get_user_meta($user_id, 'new_user_approve', true);
        // $approved = get_user_meta($user_id, '_user_approved', true);
        return $approved === 'approved' ? '✅ Approved' : '❌ Pending';
    }
    return $value;
}, 10, 3);

// 4. Add Approve/Reject bulk actions
add_filter('bulk_actions-users', function ($bulk_actions) {
    $bulk_actions['approve_users'] = 'Approve Users';
    $bulk_actions['reject_users'] = 'Reject Users';
    return $bulk_actions;
});

add_filter('handle_bulk_actions-users', function ($redirect_to, $doaction, $user_ids) {
    if ($doaction === 'approve_users') {
        foreach ($user_ids as $user_id) {
            update_user_meta($user_id, 'new_user_approve', 'approved');
            update_user_meta($user_id, 'approval_status', 'approved');
            update_user_meta($user_id, 'account_status', 'approved');
            // update_user_meta($user_id, '_user_approved', 'yes');
        }
        $redirect_to = add_query_arg('approved_users', count($user_ids), $redirect_to);
    }
    if ($doaction === 'reject_users') {
        foreach ($user_ids as $user_id) {
            wp_delete_user($user_id); // Or just update meta to rejected if you prefer
        }
        $redirect_to = add_query_arg('rejected_users', count($user_ids), $redirect_to);
    }
    return $redirect_to;
}, 10, 3);

// 6. Display Approve/Reject buttons in the new column
add_filter('manage_users_custom_column', function ($value, $column_name, $user_id) {
    if ($column_name === 'approval_action') {
        $status = get_user_meta($user_id, 'new_user_approve', true);
        // $status = get_user_meta($user_id, '_user_approved', true);

        if ($status !== 'approved') {
            $approve_url = wp_nonce_url(add_query_arg([
                'action' => 'approve_user',
                'user_id' => $user_id,
            ]), 'approve_user_' . $user_id);

            $reject_url = wp_nonce_url(add_query_arg([
                'action' => 'reject_user',
                'user_id' => $user_id,
            ]), 'reject_user_' . $user_id);

            return '<a href="' . esc_url($approve_url) . '" class="button button-primary" style="margin-right:5px;">Approve</a>' .
                   '<a href="' . esc_url($reject_url) . '" class="button button-secondary">Reject</a>';
        } else {
            return '<span style="color: green; font-weight: bold;">Approved</span>';
        }
    }

    return $value;
}, 10, 3);

// 7. Handle Approve and Reject actions
add_action('admin_init', function () {
    if (!current_user_can('edit_users')) {
        return;
    }

    if (isset($_GET['action'], $_GET['user_id']) && $_GET['action'] === 'approve_user') {
        $user_id = intval($_GET['user_id']);
        if (wp_verify_nonce($_GET['_wpnonce'], 'approve_user_' . $user_id)) {
            update_user_meta($user_id, 'new_user_approve', 'approved');
            update_user_meta($user_id, 'approval_status', 'approved');
            update_user_meta($user_id, 'account_status', 'approved');
            // update_user_meta($user_id, '_user_approved', 'yes');
            wp_redirect(add_query_arg(['user_approved_single' => 1], remove_query_arg(['action', 'user_id', '_wpnonce'])));
            exit;
        }
    }

    if (isset($_GET['action'], $_GET['user_id']) && $_GET['action'] === 'reject_user') {
        $user_id = intval($_GET['user_id']);
        if (wp_verify_nonce($_GET['_wpnonce'], 'reject_user_' . $user_id)) {
            wp_delete_user($user_id);
            wp_redirect(add_query_arg(['user_rejected_single' => 1], remove_query_arg(['action', 'user_id', '_wpnonce'])));
            exit;
        }
    }
});

// 8. Admin notice for single approve/reject
add_action('admin_notices', function () {
    if (isset($_GET['user_approved_single'])) {
        echo '<div class="notice notice-success is-dismissible"><p>User approved successfully.</p></div>';
    }
    if (isset($_GET['user_rejected_single'])) {
        echo '<div class="notice notice-warning is-dismissible"><p>User rejected and deleted successfully.</p></div>';
    }
});