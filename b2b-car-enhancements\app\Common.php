<?php
/**
 * All common functions to load in both admin and front
 */
namespace Worzen\B2B_Car_Enhancements\App;
use <PERSON><PERSON>zen\B2B_Car_Enhancements\Helper;
use Codexpert\Plugin\Base;

/**
 * if accessed directly, exit.
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * @package Plugin
 * @subpackage Common
 * <AUTHOR> <<EMAIL>>
 */
class Common extends Base {

	public $plugin;

	/**
	 * Constructor function
	 */
	public function __construct( $plugin ) {
		$this->plugin	= $plugin;
		$this->slug		= $this->plugin['TextDomain'];
		$this->name		= $this->plugin['Name'];
		$this->version	= $this->plugin['Version'];
	}

	public function user_register( $user_id ) {

		$subject = Helper::get_option( 'pua_basic', 'admin_email_subject' );
		$_notice = Helper::get_option( 'pua_basic', 'admin_email_notice' );

		update_user_meta( $user_id, 'new_user_approve', 0 );

	    $admin_email 	= get_option( 'admin_email' );
	    $user 			= get_userdata( $user_id );

	    $placeholders 	= ['%user%', '%username%', '%display_name%'];
		$replacements 	= [$user->user_email, $user->user_login, $user->display_name];
		$notice 		= str_replace( $placeholders, $replacements, $template );

	    wp_mail( $admin_email, $subject, $notice );
	}
}