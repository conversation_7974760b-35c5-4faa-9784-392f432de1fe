!function(e){e(document).ready((function(){e(".select2").select2({closeOnSelect:!1}),e(document).on("change",".dynamic-values select",(function(t){t.stopPropagation();var a=e(this),i=a.val();if(""!=i){var n=a.parents(".acf-field[data-dynamic_values]").first().find(".wp-editor-area");if(n.length>0)tinymce.editors[n.attr("id")].editorCommands.execCommand("mceInsertContent",!1,"["+i+"]"),$dvOpened=!1;else!function(e,t){var a=e;if(a){t="["+t+"]";var i=a.scrollTop,n=0,l=a.selectionStart||"0"==a.selectionStart?"ff":!!document.selection&&"ie";if("ie"==l){a.focus();var s=document.selection.createRange();s.moveStart("character",-a.value.length),n=s.text.length}else"ff"==l&&(n=a.selectionStart);var c=a.value.substring(0,n),d=a.value.substring(n,a.value.length);if(a.value=c+t+d,n+=t.length,"ie"==l){a.focus();var r=document.selection.createRange();r.moveStart("character",-a.value.length),r.moveStart("character",n),r.moveEnd("character",0),r.select()}else"ff"==l&&(a.selectionStart=n,a.selectionEnd=n,a.focus());a.scrollTop=i}}(a.parents(".dynamic-values").siblings("input[type=text], textarea").get(0),i);a.removeProp("selected").closest("select").val("").trigger("change")}})),e(document).on("focusin click",".acf-field[data-dynamic_values] input, a.dynamic-value-options, .acf-field[data-dynamic_values] textarea",(function(t){t.stopPropagation();var a=e(this),i=e(".dynamic-values");a.after(i),i.show()})),e("body").on("change","select#form-admin_form_type",(function(t){var a=e(this).parents("form").find("input#title");""==a.val()&&(a.val(e(this).find("option[value="+e(this).val()+"]").text()),a.siblings("label").addClass("screen-reader-text"))}))}));new acf.Model({id:"formFieldManager",events:{"click .add-acf-fields":"onClickAddFields"},onClickAddFields:function(t,a){let i;i=a.hasClass("add-first-field")?a.parents(".acf-field-list").eq(0):a.parent().hasClass("acf-headerbar-actions")||a.parent().hasClass("no-fields-message-inner")?e(".acf-field-list:first"):a.parent().hasClass("acf-sub-field-list-header")?a.parents(".acf-input:first").find(".acf-field-list:first"):a.closest(".acf-tfoot").siblings(".acf-field-list"),this.addField(i)},addField:function(t){var a=e("#tmpl-acf-field").html(),i=e(a),n=i.data("id"),l=acf.uniqid("field_"),s=acf.duplicate({target:i,search:n,replace:l,append:function(e,a){t.append(a)}}),c=acf.getFieldObject(s);c.prop("key",l),c.prop("ID",0),c.prop("label","ACF Fields"),c.prop("name",""),s.attr("data-key",l),s.attr("data-id",l),c.updateParent();var d=c.$input("type");c.open(),d.val("fields_select"),d.change(),this.renderFields(t),acf.doAction("add_field_object",c),acf.doAction("append_field_object",c)}})}(jQuery);