!function(e){e(document).ready((function(){e("body").on("click",".fea-delete-button",(function(a){let t=e(this);if(t.hasClass("disabled"))return;t.addClass("disabled");acf.newTooltip({confirm:!0,text:t.data("confirm"),target:t,context:t.closest(".acf-field"),confirm:function(){!function(a){if(!(e(window).off("beforeunload"),a.after('<span class="fea-loader"></span>'),$form=a.closest("form"),$form[0]||($form=a.siblings("form"),$form[0])))return;$form.find(".fea-submit-button").addClass("disabled");let t=new FormData($form[0]);let o=a.closest(".acf-field"),n=$form.data("field");n||(n=o.data("key")||a.data("key"));if(!n)return;t.append("action","frontend_admin/delete_object"),t.append("field",n),t.append("delete_object",a.data("object")),e.ajax({url:acf.get("ajaxurl"),type:"post",data:t,cache:!1,processData:!1,contentType:!1,success:function(e){if(e.success&&e.data&&e.data?.redirect){let a=e.data.redirect.replace(/&amp;/g,"&");window.location=decodeURIComponent(a)}}})}(t)},cancel:function(){t.removeClass("disabled")}})}))}))}(jQuery);