(()=>{"use strict";var t={251:(t,e,n)=>{var o=n(196),r=Symbol.for("react.element"),s=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),i=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function a(t,e,n){var o,a={},l=null,c=null;for(o in void 0!==n&&(l=""+n),void 0!==e.key&&(l=""+e.key),void 0!==e.ref&&(c=e.ref),e)s.call(e,o)&&!u.hasOwnProperty(o)&&(a[o]=e[o]);if(t&&t.defaultProps)for(o in e=t.defaultProps)void 0===a[o]&&(a[o]=e[o]);return{$$typeof:r,type:t,key:l,ref:c,props:a,_owner:i.current}}e.jsx=a,e.jsxs=a},893:(t,e,n)=>{t.exports=n(251)},196:t=>{t.exports=window.React}},e={};function n(o){var r=e[o];if(void 0!==r)return r.exports;var s=e[o]={exports:{}};return t[o](s,s.exports,n),s.exports}(()=>{const t=window.wp.blocks;var e=n(893);const o=window.wp.blockEditor,r=window.wp.components,s=window.wp.i18n,i=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":2,"name":"frontend-admin/submit-button","title":"Submit Button","description":"Displays a submit button.","category":"frontend-admin","textdomain":"frontend-admin","icon":"list-view","supports":{"align":["wide"]},"usesContext":["frontend-admin/form/form_settings","frontend-admin/form/form_key"],"attributes":{"button_text":{"type":"string","default":"Submit"}},"editorScript":"file:../../submit-button/index.js"}');(0,t.registerBlockType)(i,{edit:function(t){var n=t.attributes,i=t.setAttributes,u=(0,o.useBlockProps)();return(0,e.jsxs)("div",Object.assign({},u,{children:[(0,e.jsx)(o.InspectorControls,{children:(0,e.jsx)(r.PanelBody,{title:"Button Settings",children:(0,e.jsx)(r.TextControl,{label:(0,s.__)("Button Text","acf-frontend-form-element"),value:n.button_text,onChange:function(t){return i({button_text:t})}})})}),(0,e.jsx)("div",Object.assign({},(0,o.useBlockProps)({className:"wp-block-button"}),{children:(0,e.jsx)(r.Button,{className:"wp-block-button fea-submit-button",children:(0,e.jsx)(o.RichText,{tagName:"span",value:n.button_text||"Submit",onChange:function(t){return i({button_text:t})},placeholder:"Add text…"})})}))]}))},save:function(){return null}})})()})();