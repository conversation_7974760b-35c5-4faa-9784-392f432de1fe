{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "frontend-admin/range-field", "title": "Range Field", "description": "Displays a range field.", "category": "frontend-admin", "textdomain": "frontend-admin", "supports": {"align": ["wide"]}, "attributes": {"name": {"type": "string", "default": ""}, "label": {"type": "string", "default": "Range Field"}, "hide_label": {"type": "boolean", "default": ""}, "required": {"type": "boolean", "default": ""}, "default_value": {"type": "number", "default": ""}, "placeholder": {"type": "string", "default": ""}, "instructions": {"type": "string", "default": ""}, "prepend": {"type": "string", "default": ""}, "append": {"type": "string", "default": ""}, "min": {"type": "number", "default": "1"}, "max": {"type": "number", "default": "100"}, "step": {"type": "number", "default": ""}}, "editorScript": "file:../../range/index.js"}