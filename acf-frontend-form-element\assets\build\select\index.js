(()=>{"use strict";const e=window.wp.blocks,t=window.wp.i18n,n=window.wp.blockEditor,r=window.wp.components,l=function(e){var r=e.attributes,l=e.setAttributes,a=r.label,o=r.hide_label,c=r.required,i=r.instructions;return React.createElement("div",{className:"acf-field"},React.createElement("div",{className:"acf-label"},React.createElement("label",null,!o&&React.createElement(n.RichText,{tagName:"label",onChange:function(e){return l({label:e})},withoutInteractiveFormatting:!0,placeholder:(0,t.__)("Text Field","acf-frontend-form-element"),value:a}),c&&React.createElement("span",{className:"acf-required"},"*"))),React.createElement("div",{className:"acf-input"},i&&React.createElement(n.RichText,{tagName:"p",className:"description",onChange:function(e){return l({instructions:e})},withoutInteractiveFormatting:!0,value:i}),React.createElement("div",{className:"acf-input-wrap",style:{display:"flex",width:"100%"}},e.children)))},a=window.React;var o="acf-frontend-form-element";const c=function(e){var l=e.attributes,c=e.setAttributes,i=l.label,u=l.hide_label,s=l.required,f=l.instructions,d=function(e){return e.toLowerCase().replace(/[^a-z0-9 _]/g,"").replace(/\s+/g,"_")};return(0,a.useEffect)((function(){"field_key"in l&&!l.field_key&&c({field_key:Math.random().toString(36).substring(2,10)})}),[]),React.createElement(n.InspectorControls,{field_key:"fea-inspector-controls"},React.createElement(r.PanelBody,{title:(0,t.__)("General",o),initialOpen:!0},React.createElement(r.TextControl,{label:(0,t.__)("Label",o),value:i,onChange:function(e){return c({label:e})}}),React.createElement(r.ToggleControl,{label:(0,t.__)("Hide Label",o),checked:u,onChange:function(e){return c({hide_label:e})}}),"name"in l&&React.createElement(r.TextControl,{label:(0,t.__)("Name",o),value:l.name||d(i),onChange:function(e){return c({name:d(e)})}}),"field_key"in l&&React.createElement(r.TextControl,{label:(0,t.__)("Field Key",o),value:l.field_key,readOnly:!0,onChange:function(e){}}),React.createElement(r.TextareaControl,{label:(0,t.__)("Instructions",o),rows:"3",value:f,onChange:function(e){return c({instructions:e})}}),React.createElement(r.ToggleControl,{label:(0,t.__)("Required",o),checked:s,onChange:function(e){return c({required:e})}}),e.children))},i=window.wp.element;function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){var r,l,a,o;r=e,l=t,a=n[t],o=function(e,t){if("object"!=u(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(l),(l="symbol"==u(o)?o:String(o))in r?Object.defineProperty(r,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[l]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function d(e){return function(e){if(Array.isArray(e))return m(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return m(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var b="acf-frontend-form-element";const p=function(e){var n=e.attributes,l=e.setAttributes,a=n.choices;return React.createElement(i.Fragment,null,a.map((function(e,n){return React.createElement("div",{style:{display:"flex",justifyContent:"space-between"},key:n},React.createElement(r.TextControl,{label:(0,t.__)("Choice Label",b),value:e.label,onChange:function(e){return function(e,t){var n=d(a);e>=0&&e<n.length&&(n[e]=f(f({},n[e]),{},{label:t}),l({choices:n}))}(n,e)}}),React.createElement(r.TextControl,{label:(0,t.__)("Choice Value",b),value:e.value||"option_".concat(n+1),onChange:function(e){return function(e,t){var n=d(a);e>=0&&e<n.length&&(n[e]=f(f({},n[e]),{},{value:t}),l({choices:n}))}(n,e)}}),a.length>1&&React.createElement(r.Button,{onClick:function(){return function(e){var t=d(a);e>=0&&e<t.length&&(t.splice(e,1),l({choices:t}))}(n)}},"X"))})),React.createElement(r.Button,{onClick:function(){var e=d(a),t={label:"Option ".concat(e.length+1),value:"option_".concat(e.length+1)};e.push(t),l({choices:e})}},(0,t.__)("Add Choice",b)))};var y="acf-frontend-form-element";const g=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":2,"name":"frontend-admin/select-field","title":"Select Field","description":"Displays a select field.","category":"frontend-admin","textdomain":"frontend-admin","icon":"list-view","supports":{"align":["wide"]},"attributes":{"name":{"type":"string","default":""},"label":{"type":"string","default":"Select"},"hide_label":{"type":"boolean","default":""},"required":{"type":"boolean","default":""},"instructions":{"type":"string","default":""},"layout":{"type":"string","default":"vertical"},"choices":{"type":"array","default":[{"value":"red","label":"Red"},{"value":"blue","label":"Blue"}]},"default_value":{"type":"string","default":""},"ui":{"type":"boolean","default":false},"ajax":{"type":"boolean","default":false},"allow_null":{"type":"boolean","default":false},"multiple":{"type":"boolean","default":false},"return_format":{"type":"string","default":"value"}},"editorScript":"file:../../select/index.js"}');(0,e.registerBlockType)(g,{edit:function(e){var a=e.attributes,o=e.setAttributes,i=(a.default_value,a.choices),u=a.ajax,s=a.ui,f=a.allow_null,d=a.multiple,m=a.placeholder,b=(0,n.useBlockProps)();return React.createElement("div",b,React.createElement(c,e,React.createElement(r.ToggleControl,{label:(0,t.__)("Allow Null",y),checked:f,onChange:function(e){return o({allow_null:e})}}),React.createElement(r.TextControl,{label:(0,t.__)("Placeholder",y),value:m,onChange:function(e){return o({placeholder:e})}}),React.createElement(r.ToggleControl,{label:(0,t.__)("Select Multiple Values",y),checked:d,onChange:function(e){return o({multiple:e})}}),React.createElement(r.ToggleControl,{label:(0,t.__)("Stylized UI",y),checked:s,onChange:function(e){return o({ui:e})}}),s&&React.createElement(r.ToggleControl,{label:(0,t.__)("Lazy Load Choices",y),checked:u,onChange:function(e){return o({ajax:e})}}),React.createElement(p,e)),React.createElement(l,e,React.createElement("select",null,i.map((function(e,t){return React.createElement("option",{key:t,value:e.value},e.label)})))))},save:function(){return null}})})();