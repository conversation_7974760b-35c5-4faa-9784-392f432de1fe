(()=>{"use strict";const e=window.wp.blocks,t=window.wp.i18n,n=window.wp.blockEditor,a=window.wp.components,l=function(e){var a=e.attributes,l=e.setAttributes,r=a.label,c=a.hide_label,i=a.required,o=a.instructions;return React.createElement("div",{className:"acf-field"},React.createElement("div",{className:"acf-label"},React.createElement("label",null,!c&&React.createElement(n.RichText,{tagName:"label",onChange:function(e){return l({label:e})},withoutInteractiveFormatting:!0,placeholder:(0,t.__)("Text Field","acf-frontend-form-element"),value:r}),i&&React.createElement("span",{className:"acf-required"},"*"))),React.createElement("div",{className:"acf-input"},o&&React.createElement(n.RichText,{tagName:"p",className:"description",onChange:function(e){return l({instructions:e})},withoutInteractiveFormatting:!0,value:o}),React.createElement("div",{className:"acf-input-wrap",style:{display:"flex",width:"100%"}},e.children)))},r=window.React;var c="acf-frontend-form-element";const i=function(e){var l=e.attributes,i=e.setAttributes,o=l.label,u=l.hide_label,d=l.required,s=l.instructions,m=function(e){return e.toLowerCase().replace(/[^a-z0-9 _]/g,"").replace(/\s+/g,"_")};return(0,r.useEffect)((function(){"field_key"in l&&!l.field_key&&i({field_key:Math.random().toString(36).substring(2,10)})}),[]),React.createElement(n.InspectorControls,{field_key:"fea-inspector-controls"},React.createElement(a.PanelBody,{title:(0,t.__)("General",c),initialOpen:!0},React.createElement(a.TextControl,{label:(0,t.__)("Label",c),value:o,onChange:function(e){return i({label:e})}}),React.createElement(a.ToggleControl,{label:(0,t.__)("Hide Label",c),checked:u,onChange:function(e){return i({hide_label:e})}}),"name"in l&&React.createElement(a.TextControl,{label:(0,t.__)("Name",c),value:l.name||m(o),onChange:function(e){return i({name:m(e)})}}),"field_key"in l&&React.createElement(a.TextControl,{label:(0,t.__)("Field Key",c),value:l.field_key,readOnly:!0,onChange:function(e){}}),React.createElement(a.TextareaControl,{label:(0,t.__)("Instructions",c),rows:"3",value:s,onChange:function(e){return i({instructions:e})}}),React.createElement(a.ToggleControl,{label:(0,t.__)("Required",c),checked:d,onChange:function(e){return i({required:e})}}),e.children))};var o="acf-frontend-form-element";const u=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":2,"name":"frontend-admin/number-field","title":"Number Field","description":"Displays a number field.","category":"frontend-admin","textdomain":"frontend-admin","supports":{"align":["wide"]},"attributes":{"name":{"type":"string","default":""},"label":{"type":"string","default":"Number Field"},"hide_label":{"type":"boolean","default":""},"required":{"type":"boolean","default":""},"default_value":{"type":"number","default":""},"placeholder":{"type":"string","default":""},"instructions":{"type":"string","default":""},"prepend":{"type":"string","default":""},"append":{"type":"string","default":""},"min":{"type":"number","default":"1"},"max":{"type":"number","default":"100"},"step":{"type":"number","default":""}},"editorScript":"file:../../number/index.js"}');(0,e.registerBlockType)(u,{edit:function(e){var r=e.attributes,c=e.setAttributes,u=r.default_value,d=r.placeholder,s=r.prepend,m=r.append,p=r.min,f=r.max,b=r.step,_=(0,n.useBlockProps)();return React.createElement("div",_,React.createElement(i,e,React.createElement(a.TextControl,{type:"number",label:(0,t.__)("Default Value",o),value:u,onChange:function(e){return c({default_value:e})}}),React.createElement(a.TextControl,{label:(0,t.__)("Placeholder",o),value:d,onChange:function(e){return c({placeholder:e})}}),React.createElement(a.TextControl,{label:(0,t.__)("Prepend",o),value:s,onChange:function(e){return c({prepend:e})}}),React.createElement(a.TextControl,{label:(0,t.__)("Append",o),value:m,onChange:function(e){return c({append:e})}}),React.createElement(a.TextControl,{type:"number",label:(0,t.__)("Minimum Value",o),value:p||1,onChange:function(e){return c({min:e})}}),React.createElement(a.TextControl,{type:"number",label:(0,t.__)("Maximum Value",o),value:f||100,onChange:function(e){return c({max:e})}}),React.createElement(a.TextControl,{type:"number",label:(0,t.__)("Step",o),value:b,onChange:function(e){return c({step:e})}})),React.createElement(l,e,s&&React.createElement("span",{className:"acf-input-prepend"},s),React.createElement("input",{type:"number",min:p,max:f,step:b,placeholder:d,value:u,onChange:function(e){c({default_value:e.target.value})},style:{width:"auto",flexGrow:1}}),m&&React.createElement("span",{className:"acf-input-append"},m)))},save:function(){return null}})})();