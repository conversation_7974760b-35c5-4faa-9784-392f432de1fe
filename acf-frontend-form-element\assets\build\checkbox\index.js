(()=>{"use strict";const e=window.wp.blocks,t=window.wp.i18n,n=window.wp.blockEditor,r=window.wp.components,a=function(e){var r=e.attributes,a=e.setAttributes,l=r.label,o=r.hide_label,c=r.required,i=r.instructions;return React.createElement("div",{className:"acf-field"},React.createElement("div",{className:"acf-label"},React.createElement("label",null,!o&&React.createElement(n.RichText,{tagName:"label",onChange:function(e){return a({label:e})},withoutInteractiveFormatting:!0,placeholder:(0,t.__)("Text Field","acf-frontend-form-element"),value:l}),c&&React.createElement("span",{className:"acf-required"},"*"))),React.createElement("div",{className:"acf-input"},i&&React.createElement(n.RichText,{tagName:"p",className:"description",onChange:function(e){return a({instructions:e})},withoutInteractiveFormatting:!0,value:i}),React.createElement("div",{className:"acf-input-wrap",style:{display:"flex",width:"100%"}},e.children)))},l=window.React;var o="acf-frontend-form-element";const c=function(e){var a=e.attributes,c=e.setAttributes,i=a.label,u=a.hide_label,s=a.required,f=a.instructions,m=function(e){return e.toLowerCase().replace(/[^a-z0-9 _]/g,"").replace(/\s+/g,"_")};return(0,l.useEffect)((function(){"field_key"in a&&!a.field_key&&c({field_key:Math.random().toString(36).substring(2,10)})}),[]),React.createElement(n.InspectorControls,{field_key:"fea-inspector-controls"},React.createElement(r.PanelBody,{title:(0,t.__)("General",o),initialOpen:!0},React.createElement(r.TextControl,{label:(0,t.__)("Label",o),value:i,onChange:function(e){return c({label:e})}}),React.createElement(r.ToggleControl,{label:(0,t.__)("Hide Label",o),checked:u,onChange:function(e){return c({hide_label:e})}}),"name"in a&&React.createElement(r.TextControl,{label:(0,t.__)("Name",o),value:a.name||m(i),onChange:function(e){return c({name:m(e)})}}),"field_key"in a&&React.createElement(r.TextControl,{label:(0,t.__)("Field Key",o),value:a.field_key,readOnly:!0,onChange:function(e){}}),React.createElement(r.TextareaControl,{label:(0,t.__)("Instructions",o),rows:"3",value:f,onChange:function(e){return c({instructions:e})}}),React.createElement(r.ToggleControl,{label:(0,t.__)("Required",o),checked:s,onChange:function(e){return c({required:e})}}),e.children))},i=window.wp.element;function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){var r,a,l,o;r=e,a=t,l=n[t],o=function(e,t){if("object"!=u(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(a),(a="symbol"==u(o)?o:String(o))in r?Object.defineProperty(r,a,{value:l,enumerable:!0,configurable:!0,writable:!0}):r[a]=l})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e){return function(e){if(Array.isArray(e))return b(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return b(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?b(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var d="acf-frontend-form-element";const p=function(e){var n=e.attributes,a=e.setAttributes,l=n.choices;return React.createElement(i.Fragment,null,l.map((function(e,n){return React.createElement("div",{style:{display:"flex",justifyContent:"space-between"},key:n},React.createElement(r.TextControl,{label:(0,t.__)("Choice Label",d),value:e.label,onChange:function(e){return function(e,t){var n=m(l);e>=0&&e<n.length&&(n[e]=f(f({},n[e]),{},{label:t}),a({choices:n}))}(n,e)}}),React.createElement(r.TextControl,{label:(0,t.__)("Choice Value",d),value:e.value||"option_".concat(n+1),onChange:function(e){return function(e,t){var n=m(l);e>=0&&e<n.length&&(n[e]=f(f({},n[e]),{},{value:t}),a({choices:n}))}(n,e)}}),l.length>1&&React.createElement(r.Button,{onClick:function(){return function(e){var t=m(l);e>=0&&e<t.length&&(t.splice(e,1),a({choices:t}))}(n)}},"X"))})),React.createElement(r.Button,{onClick:function(){var e=m(l),t={label:"Option ".concat(e.length+1),value:"option_".concat(e.length+1)};e.push(t),a({choices:e})}},(0,t.__)("Add Choice",d)))};var h="acf-frontend-form-element";const y=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":2,"name":"frontend-admin/checkbox-field","title":"Checkbox Field","description":"Displays a checkbox field.","category":"frontend-admin","textdomain":"frontend-admin","icon":"list-view","supports":{"align":["wide"]},"attributes":{"name":{"type":"string","default":""},"label":{"type":"string","default":"Checkbox"},"hide_label":{"type":"boolean","default":""},"required":{"type":"boolean","default":""},"instructions":{"type":"string","default":""},"layout":{"type":"string","default":"vertical"},"choices":{"type":"array","default":[{"value":"red","label":"Red"},{"value":"blue","label":"Blue"}]},"default_value":{"type":"string","default":""},"allow_custom":{"type":"boolean","default":false},"save_custom":{"type":"boolean","default":false},"toggle":{"type":"boolean","default":false},"return_format":{"type":"string","default":"value"}},"editorScript":"file:../../checkbox/index.js"}');(0,e.registerBlockType)(y,{edit:function(e){var l=e.attributes,o=e.setAttributes,i=l.default_value,u=l.layout,s=l.choices,f=l.allow_custom,m=l.save_custom,b=l.toggle,d=(0,n.useBlockProps)();return React.createElement("div",d,React.createElement(c,e,React.createElement(r.RadioControl,{label:(0,t.__)("Layout",h),selected:u,options:[{label:"Horizontal",value:"horizontal"},{label:"Vertical",value:"vertical"}],onChange:function(e){return o({layout:e})}}),React.createElement(r.ToggleControl,{label:(0,t.__)("Allow Custom Choice",h),checked:f,onChange:function(e){return o({allow_custom:e})}}),f&&React.createElement(r.ToggleControl,{label:(0,t.__)("Save Custom Choice",h),checked:m,onChange:function(e){return o({save_custom:e})}}),React.createElement(r.ToggleControl,{label:(0,t.__)("Toggle All",h),checked:b,onChange:function(e){return o({toggle:e})}}),React.createElement(p,e)),React.createElement(a,e,React.createElement("ul",{className:"acf-checkbox-list ".concat("horizontal"===u?"acf-hl":"acf-bl")},s.map((function(e,t){return React.createElement("li",{key:t},React.createElement("input",{type:"checkbox",value:e.value,checked:i.includes(e.value),onChange:function(t){return handleCheckboxChange(t,e.value)}}),e.label)})))))},save:function(){return null}})})();