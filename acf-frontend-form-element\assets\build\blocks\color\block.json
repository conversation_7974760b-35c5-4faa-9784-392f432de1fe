{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "frontend-admin/color-field", "title": "Color Field", "description": "Displays a color field.", "category": "frontend-admin", "textdomain": "frontend-admin", "icon": "list-view", "supports": {"align": ["wide"]}, "attributes": {"name": {"type": "string", "default": ""}, "label": {"type": "string", "default": "Text Field"}, "hide_label": {"type": "boolean", "default": ""}, "required": {"type": "boolean", "default": ""}, "default_value": {"type": "string", "default": ""}, "instructions": {"type": "string", "default": ""}}, "editorScript": "file:../../color/index.js"}