let pc_modal = ( show = true ) => {
	if(show) {
		jQuery('#plugin-client-modal').show();
	}
	else {
		jQuery('#plugin-client-modal').hide();
	}
}

jQuery(function($){
    const customFields = `
        <div class="xoo-aff-group xoo-aff-cont-text one xoo-aff-cont-required xoo_el_reg_b2b_phone_cont">
            <div class="xoo-aff-input-group">
                <span class="xoo-aff-input-icon fas fa-phone"></span>
                <input type="text" class="xoo-aff-required xoo-aff-text" name="xoo_el_reg_b2b_phone" placeholder="Phone Number" required>
            </div>
        </div>

        <div class="xoo-aff-group xoo-aff-cont-text one xoo-aff-cont-required xoo_el_reg_b2b_vat_cont">
            <div class="xoo-aff-input-group">
                <span class="xoo-aff-input-icon fas fa-receipt"></span>
                <input type="text" class="xoo-aff-required xoo-aff-text" name="xoo_el_reg_b2b_vat" placeholder="VAT Number" required>
            </div>
        </div>

        <div class="xoo-aff-group xoo-aff-cont-text one xoo-aff-cont-required xoo_el_reg_b2b_company_cont">
            <div class="xoo-aff-input-group">
                <span class="xoo-aff-input-icon fas fa-building"></span>
                <input type="text" class="xoo-aff-required xoo-aff-text" name="xoo_el_reg_b2b_company" placeholder="Commercial Name" required>
            </div>
        </div>
    `;

    // Find the password field wrapper
    const passwordField = $('form.xoo-el-action-form.xoo-el-form-register .xoo_el_reg_pass_cont');

    // Inject custom fields before the password field
    if (passwordField.length > 0) {
        $(customFields).insertBefore(passwordField);
    }

})