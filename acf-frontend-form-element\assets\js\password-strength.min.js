function checkPasswordStrength(a,b,c){a=a.val();b.removeClass("weak short bad good strong");c=wp.passwordStrength.meter(a,c,a);b.siblings("input.password-strength").val(c);switch(c){case 2:b.addClass("bad").html(pwsL10n.bad);break;case 3:b.addClass("good").html(pwsL10n.good);break;case 4:b.addClass("strong").html(pwsL10n.strong);break;case 5:b.addClass("short").html(pwsL10n.mismatch);break;default:b.addClass("short").html(pwsL10n["short"])}return c}
function checkPasswordsMatch(a,b,c){b=b.val();a=a.val();c.removeClass("weak strong short");b==a?c.addClass("strong").html(acf.__("Passwords Match")):c.addClass("short").html(pwsL10n.mismatch)}
jQuery(document).ready(function(a){a("body").on("keyup",".password_main input",function(b){checkPasswordStrength(a(this),a(this).parents(".acf-input-wrap").siblings(".pass-strength-result"),[])});a("body").on("keyup",".acf-field-user-password-confirm input",function(b){b=a(this).parents(".acf-field-user-password-confirm").siblings(".password_main").find("input[type=password]");checkPasswordsMatch(a(this),b,a(this).parents(".acf-input-wrap").siblings(".pass-strength-result"))})});