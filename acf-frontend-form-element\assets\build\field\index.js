(()=>{"use strict";const e=window.wp.blocks,t=window.wp.i18n,n=window.wp.blockEditor,a=window.wp.components,l=function(e){var a=e.attributes,l=e.setAttributes,r=a.label,c=a.hide_label,o=a.required,i=a.instructions;return React.createElement("div",{className:"acf-field"},React.createElement("div",{className:"acf-label"},React.createElement("label",null,!c&&React.createElement(n.RichText,{tagName:"label",onChange:function(e){return l({label:e})},withoutInteractiveFormatting:!0,placeholder:(0,t.__)("Text Field","acf-frontend-form-element"),value:r}),o&&React.createElement("span",{className:"acf-required"},"*"))),React.createElement("div",{className:"acf-input"},i&&React.createElement(n.RichText,{tagName:"p",className:"description",onChange:function(e){return l({instructions:e})},withoutInteractiveFormatting:!0,value:i}),React.createElement("div",{className:"acf-input-wrap",style:{display:"flex",width:"100%"}},e.children)))},r=window.React;var c="acf-frontend-form-element";const o=function(e){var l=e.attributes,o=e.setAttributes,i=l.label,u=l.hide_label,s=l.required,d=l.instructions,f=function(e){return e.toLowerCase().replace(/[^a-z0-9 _]/g,"").replace(/\s+/g,"_")};return(0,r.useEffect)((function(){"field_key"in l&&!l.field_key&&o({field_key:Math.random().toString(36).substring(2,10)})}),[]),React.createElement(n.InspectorControls,{field_key:"fea-inspector-controls"},React.createElement(a.PanelBody,{title:(0,t.__)("General",c),initialOpen:!0},React.createElement(a.TextControl,{label:(0,t.__)("Label",c),value:i,onChange:function(e){return o({label:e})}}),React.createElement(a.ToggleControl,{label:(0,t.__)("Hide Label",c),checked:u,onChange:function(e){return o({hide_label:e})}}),"name"in l&&React.createElement(a.TextControl,{label:(0,t.__)("Name",c),value:l.name||f(i),onChange:function(e){return o({name:f(e)})}}),"field_key"in l&&React.createElement(a.TextControl,{label:(0,t.__)("Field Key",c),value:l.field_key,readOnly:!0,onChange:function(e){}}),React.createElement(a.TextareaControl,{label:(0,t.__)("Instructions",c),rows:"3",value:d,onChange:function(e){return o({instructions:e})}}),React.createElement(a.ToggleControl,{label:(0,t.__)("Required",c),checked:s,onChange:function(e){return o({required:e})}}),e.children))};var i="acf-frontend-form-element";const u=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":2,"name":"frontend-admin/form-field","title":"Form Field","description":"Displays a form field.","category":"frontend-admin","textdomain":"frontend-admin","icon":"list-view","supports":{"align":["wide"]},"attributes":{"name":{"type":"string","default":""},"key":{"type":"string","default":""},"label":{"type":"string","default":"Text Field"},"hide_label":{"type":"boolean","default":""},"required":{"type":"boolean","default":""}},"variations":"file:./field-blocks.php","editorScript":"file:../../field/index.js"}');(0,e.registerBlockType)(u,{edit:function(e){var r=e.attributes,c=e.setAttributes,u=r.default_value,s=r.placeholder,d=r.prepend,f=r.append,m=r.maxlength,p=(0,n.useBlockProps)();return React.createElement("div",p,React.createElement(o,e,React.createElement(a.TextControl,{type:"text",maxLength:m,label:(0,t.__)("Default Value",i),value:u,onChange:function(e){return c({default_value:e})}}),React.createElement(a.TextControl,{label:(0,t.__)("Placeholder",i),value:s,onChange:function(e){return c({placeholder:e})}}),React.createElement(a.TextControl,{label:(0,t.__)("Prepend",i),value:d,onChange:function(e){return c({prepend:e})}}),React.createElement(a.TextControl,{label:(0,t.__)("Append",i),value:f,onChange:function(e){return c({append:e})}}),React.createElement(a.TextControl,{type:"number",label:(0,t.__)("Character Limit",i),value:m,onChange:function(e){return c({maxlength:e})}}),"post_slug"in r&&React.createElement(a.ToggleControl,{label:(0,t.__)("Use as Post Slug",i),checked:r.post_slug,onChange:function(e){return c({post_slug:e})}})),React.createElement(l,e,d&&React.createElement("span",{className:"acf-input-prepend"},d),React.createElement("input",{type:"text",maxLength:m,placeholder:s,value:u,onChange:function(e){c({default_value:e.target.value})},style:{width:"auto",flexGrow:1}}),f&&React.createElement("span",{className:"acf-input-append"},f)))},save:function(){return null}})})();