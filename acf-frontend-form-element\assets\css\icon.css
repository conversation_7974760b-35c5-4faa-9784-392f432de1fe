@font-face {
  font-family: 'frontend-icon';
  src:  url('fonts/frontend-icon.eot?hgrvqz');
  src:  url('fonts/frontend-icon.eot?hgrvqz#iefix') format('embedded-opentype'),
	url('fonts/frontend-icon.ttf?hgrvqz') format('truetype'),
	url('fonts/frontend-icon.woff?hgrvqz') format('woff'),
	url('fonts/frontend-icon.svg?hgrvqz#frontend-icon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.dashicons-frontend:before,
.icon-frontend-forms:before {
  content: "\e900";
  font-family: 'frontend-icon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.dashicons-frontend:before{
  width: 30px;
}

.elementor-panel .elementor-element .icon i[class*="frontend-icon"]:after {
  content: '\e900';
  position: absolute;
  top: 5px;
  right: 2px;
  z-index: 1;
  /*color: #e52600;*/
  background: transparent;
  font-size: 15px;
  font-family: "frontend-icon";
  font-style: normal;
  padding: 0.2em 0.5em;
  color: #e6e9ec;
  /*border-width: 0 0 1px 1px;
  border-color: #eee;
  border-radius: 50%;
  border-style: solid;*/

  transition: color 0.5s ease;
}
.elementor-panel .elementor-element:hover .icon i[class*="frontend-icon"]:after {
  color: #1c84a8;
}