!function(e){e(document).on("click",".copy-shortcode",(function(o){var c="["+e(this).data("prefix")+"="+e(this).data("value")+"]";navigator.clipboard.writeText(c);var t=e(this).html();e(this).addClass("copied-text").html(t.replace(acf.__("Copy Code"),acf.__("Code Copied"))).css({"background-color":"#4BB543",color:"#fff"}),setTimeout((function(){e("body").find(".copied-text").removeClass("copied-text").html(t.replace(acf.__("Code Copied"),acf.__("Copy Code"))).css({"background-color":"#fff",color:"#000"})}),1e3)})),acf.addAction("add_field_object",(function(e){var o=e.get("key").replace("field_",""),c=e.$el.find(".copy-shortcode");c.attr("data-value",o),c.siblings("code").html(c.siblings("code").html().replace(/acfcloneindex/g,o))}),12)}(jQuery);